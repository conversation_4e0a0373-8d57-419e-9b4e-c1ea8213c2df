import { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { AuctionState } from "@prisma/client";
import { getCurrentUser } from "@/lib/api-auth";
import { createSuccessResponse, handleApiError, ApiResponses } from "@/lib/api-responses";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user using centralized utility
    const user = await getCurrentUser(request);

    const { bidId } = await request.json();

    if (!bidId) {
      return ApiResponses.badRequest(
        "ID de oferta requerido",
        "BID_ID_REQUIRED"
      );
    }

    // Verify auction exists and belongs to the user
    const auction = await db.auction.findFirst({
      where: {
        id: params.id,
        accountHolder: {
          userId: user.id,
        },
        status: AuctionState.CLOSED, // Only closed auctions can have offers selected
      },
      include: {
        bids: {
          where: {
            id: bidId,
          },
        },
      },
    });

    if (!auction) {
      return ApiResponses.notFound(
        "Subasta no encontrada o no autorizada",
        "AUCTION_NOT_FOUND"
      );
    }

    if (auction.bids.length === 0) {
      return ApiResponses.notFound(
        "Oferta no encontrada en esta subasta",
        "BID_NOT_FOUND"
      );
    }

    // Update auction with selected bid and signature confirmation
    const updatedAuction = await db.auction.update({
      where: {
        id: params.id,
      },
      data: {
        selectedBidId: bidId,
        selectedAt: new Date(),
        signatureConfirmed: true,
        signatureConfirmedAt: new Date(),
      },
      include: {
        bids: {
          where: {
            id: bidId,
          },
          include: {
            broker: true,
            document: true,
          },
        },
        policy: true,
        selectedBid: {
          include: {
            broker: {
              include: {
                user: true,
              },
            },
          },
        },
      },
    });

    return createSuccessResponse(
      { auction: updatedAuction },
      "Oferta seleccionada exitosamente"
    );
  } catch (error) {
    return handleApiError(error);
  }
}