"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { FileUpload } from "@/components/ui/file-upload";
import { formatCurrency } from "@/lib/utils";
import { Upload, FileText, CheckCircle } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

interface AuctionBid {
  id: string;
  annualPremium: number;
  brokerName: string;
  brokerCompany: string;
  createdAt: string;
  hasDocument: boolean;
  bidCoverages?: any[];
  brokerPhone?: string | null;
  brokerEmail?: string | null;
  brokerIdentifier?: string | null;
  quoteDocument?: {
    id: string;
    fileName: string | null;
    fileSize: number | null;
    uploadedAt: string;
    url: string;
  } | null;
}

interface PolicyUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpload: (file: File) => void;
  onBack?: () => void;
  selectedBid: AuctionBid | null;
  auctionId: string;
  loading?: boolean;
}

export function PolicyUploadModal({
  isOpen,
  onClose,
  onUpload,
  onBack,
  selectedBid,
  auctionId,
  loading = false,
}: PolicyUploadModalProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const { toast } = useToast();

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
  };

  const handleFileRemove = () => {
    setSelectedFile(null);
  };

  const handleUpload = () => {
    if (!selectedFile) {
      toast({
        title: "Archivo requerido",
        description: "Debe seleccionar un archivo para continuar",
        variant: "destructive",
      });
      return;
    }

    if (!termsAccepted) {
      toast({
        title: "Confirmación requerida",
        description: "Debe aceptar los términos para continuar",
        variant: "destructive",
      });
      return;
    }

    onUpload(selectedFile);
  };

  const handleClose = () => {
    setSelectedFile(null);
    setTermsAccepted(false);
    onClose();
  };

  if (!selectedBid) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="w-12 h-12 bg-[#3ea050] rounded-lg flex items-center justify-center">
              <Upload className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Subir Nueva Póliza</h2>
              <p className="text-sm text-gray-600 mt-1">Sube tu nueva póliza firmada con {selectedBid.brokerCompany}</p>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">

          {/* Document Upload Section */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-gray-900">Documento de Póliza (PDF)</h3>
            
            <FileUpload
              onFileSelect={handleFileSelect}
              onFileRemove={handleFileRemove}
              acceptedFileTypes={["application/pdf", "image/jpeg", "image/png"]}
              maxFileSize={10 * 1024 * 1024} // 10MB
              title="Arrastra tu póliza aquí o haz clic para seleccionar"
              description="Solo archivos PDF • Máximo 10MB"
              buttonText="Seleccionar archivo"
              compact={true}
            />
          </div>

          {/* Important Information */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <span className="font-medium text-[#92400e]">Importante</span>
            </div>
            <ul className="space-y-2 text-sm text-[#92400e]">
              <li>• Una vez subida la póliza, el proceso será irreversible</li>
              <li>• Asegúrese de que el documento esté completo y firmado</li>
              <li>• Completa todos los campos</li>
            </ul>
          </div>

          {/* Confirmation Checkbox */}
          <div className="flex items-start space-x-3">
            <Checkbox
              id="upload-confirmation"
              checked={termsAccepted}
              onCheckedChange={(checked) => setTermsAccepted(checked as boolean)}
              className="mt-1"
            />
            <Label htmlFor="upload-confirmation" className="text-sm leading-relaxed text-gray-700">
              Confirmo que el documento adjunto es la póliza firmada con <strong>{selectedBid.brokerName}</strong> de la aseguradora <strong>{selectedBid.brokerCompany}</strong> y que toda la información es correcta y completa.
            </Label>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              variant="outline"
              onClick={onBack || handleClose}
              disabled={loading}
              className="flex-1 border-gray-300 text-black hover:bg-gray-50 hover:text-black"
            >
              {onBack ? "Volver" : "Cancelar"}
            </Button>
            <Button
              onClick={handleUpload}
              disabled={!selectedFile || !termsAccepted || loading}
              className="flex-1 bg-[#3ea050] hover:bg-[#3ea050]/90 text-white font-medium"
            >
              {loading ? "Subiendo..." : "Finalizar y Confirmar"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}