"use client";

import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { formatCurrency, formatDate } from "@/lib/utils";
import { CheckCircle, Calendar, PiggyBank, Shield, User, FileText } from "lucide-react";

interface AuctionBid {
  id: string;
  annualPremium: number;
  brokerName: string;
  brokerCompany: string;
  createdAt: string;
  hasDocument: boolean;
  bidCoverages?: any[];
  brokerPhone?: string | null;
  brokerEmail?: string | null;
  brokerIdentifier?: string | null;
  quoteDocument?: {
    id: string;
    fileName: string | null;
    fileSize: number | null;
    uploadedAt: string;
    url: string;
  } | null;
}

interface CompletionModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedBid: AuctionBid | null;
  currentPremium: number;
  nextRenewalDate?: string;
  auctionId: string;
}

export function CompletionModal({
  isOpen,
  onClose,
  selectedBid,
  currentPremium,
  nextRenewalDate,
  auctionId,
}: CompletionModalProps) {
  if (!selectedBid) return null;

  const savings = currentPremium - selectedBid.annualPremium;
  const savingsPercentage = ((savings / currentPremium) * 100).toFixed(1);
  const renewalDate = nextRenewalDate ? new Date(nextRenewalDate) : new Date(Date.now() + 365 * 24 * 60 * 60 * 1000);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-green-600" />
            ¡Proceso Completado Exitosamente!
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Success Message */}
          <Card className="border-green-200 bg-green-50">
            <CardContent className="pt-6">
              <div className="text-center space-y-2">
                <CheckCircle className="h-12 w-12 text-green-600 mx-auto" />
                <h3 className="text-lg font-semibold text-green-800">
                  Su nueva póliza ha sido procesada
                </h3>
                <p className="text-green-700">
                  El proceso de selección de mejor oferta se ha completado correctamente
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Savings Summary */}
          {savings > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PiggyBank className="h-5 w-5 text-green-600" />
                  Resumen de Ahorros
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <p className="text-sm text-muted-foreground">Prima Anterior</p>
                    <p className="text-xl font-bold text-red-600">
                      {formatCurrency(currentPremium)}
                    </p>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <p className="text-sm text-muted-foreground">Nueva Prima</p>
                    <p className="text-xl font-bold text-green-600">
                      {formatCurrency(selectedBid.annualPremium)}
                    </p>
                  </div>
                </div>
                
                <div className="text-center p-4 bg-blue-50 rounded-lg border-2 border-blue-200">
                  <p className="text-sm text-muted-foreground">Ahorro Total</p>
                  <p className="text-3xl font-bold text-blue-600">
                    {formatCurrency(savings)}
                  </p>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800 mt-2">
                    {savingsPercentage}% de descuento
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}

          {/* New Insurer Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-blue-600" />
                Nueva Aseguradora
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <User className="h-8 w-8 text-blue-600" />
                  <div>
                    <p className="font-medium">{selectedBid.brokerName}</p>
                    <p className="text-sm text-muted-foreground">
                      Agente: {selectedBid.brokerCompany}
                    </p>
                  </div>
                </div>
                <Badge variant="outline" className="border-green-500 text-green-700">
                  Activa
                </Badge>
              </div>
              
              <div className="grid grid-cols-2 gap-4 pt-2">
                <div>
                  <p className="text-sm text-muted-foreground">Prima Anual</p>
                  <p className="font-semibold">{formatCurrency(selectedBid.annualPremium)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Próxima Renovación</p>
                  <p className="font-semibold">{formatDate(renewalDate)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Separator />

          {/* Next Steps */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-blue-600" />
                Próximos Pasos
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium">Póliza Activada</p>
                  <p className="text-sm text-muted-foreground">
                    Su nueva póliza está ahora activa y aparecerá en su cartera
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <FileText className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium">Documentación</p>
                  <p className="text-sm text-muted-foreground">
                    Recibirá una copia de la póliza firmada por correo electrónico
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <Calendar className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium">Recordatorio de Renovación</p>
                  <p className="text-sm text-muted-foreground">
                    Le notificaremos 30 días antes de la fecha de renovación
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              variant="outline"
              onClick={() => window.location.href = '/account-holder/policies'}
              className="flex-1"
            >
              Ver Mis Pólizas
            </Button>
            <Button
              onClick={onClose}
              className="flex-1"
            >
              Cerrar
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}