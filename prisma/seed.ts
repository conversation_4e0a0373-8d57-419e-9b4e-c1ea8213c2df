import { PrismaClient, Role, Gender, InsurerCompany, PolicyStatus, AuctionState, DocumentType } from '@prisma/client';
import { createClient } from '@supabase/supabase-js';
import { calculateWorkingHoursClosedAt, DEFAULT_AUCTION_DURATION_HOURS } from '../src/lib/auction/working-hours';
import {
  allEnumCoverages
} from './sample-structered-coverages';

const prisma = new PrismaClient();

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Function to get simplified coverage data (20 coverages per policy)
function getSimplifiedCoverages() {
  // Use only the first 20 coverages from the comprehensive set
  return allEnumCoverages.slice(0, 20);
}

// Normalization function for mandatory liability coverage
function normalizeMandatoryLiability(coverage: any) {
  if (coverage.type === 'MANDATORY_LIABILITY') {
    return {
      ...coverage,
      liabilityBodilyCap: 70000000,
      liabilityPropertyCap: 15000000,
    };
  }
  return coverage;
}

// Helper function to create bid coverages efficiently
async function createBidCoveragesForBid(
  prisma: any,
  bidId: string,
  policyCoverages: any[],
  strategy: any,
  brokerIndex: number
) {
  const bidCoverageData: any[] = [];

  // Process policy coverages
  for (const coverage of policyCoverages) {
    const isIncluded = Math.random() < strategy.includeRate;

    if (isIncluded) {
      // Enhanced coverage terms based on broker strategy
      const enhancedLimit = coverage.limit ? Math.round(Number(coverage.limit) * strategy.limitMultiplier * 100) / 100 : coverage.limit;
      const enhancedDeductible = coverage.deductible ? Math.round(Number(coverage.deductible) * strategy.deductibleMultiplier * 100) / 100 : coverage.deductible;

      // Add broker-specific enhancements based on coverage type
      let enhancedDescription = coverage.description || '';
      let customName = coverage.customName;

      switch (coverage.type) {
        case 'MANDATORY_LIABILITY':
          enhancedDescription = `${enhancedDescription} - ${strategy.description} con gestión 24/7`;
          break;
        case 'VEHICLE_DAMAGE':
          enhancedDescription = `${enhancedDescription} - ${strategy.description}, talleres concertados premium`;
          break;
        case 'THEFT':
          enhancedDescription = `${enhancedDescription} - ${strategy.description}, localización GPS incluida`;
          break;
        case 'FIRE':
          enhancedDescription = `${enhancedDescription} - ${strategy.description}, peritaje express`;
          break;
        case 'GLASS_BREAKAGE':
          enhancedDescription = `${enhancedDescription} - ${strategy.description}, reparación a domicilio`;
          break;
        case 'LEGAL_DEFENSE':
          enhancedDescription = `${enhancedDescription} - ${strategy.description}, asesoría jurídica especializada`;
          break;
        default:
          enhancedDescription = `${enhancedDescription} - ${strategy.description}`;
      }

      bidCoverageData.push({
        bidId: bidId,
        type: coverage.type,
        customName: customName,
        limit: coverage.type === 'MANDATORY_LIABILITY' ? null : enhancedLimit,
        limitIsUnlimited: coverage.type === 'LEGAL_DEFENSE',
        limitIsFullCost: ['VEHICLE_DAMAGE', 'THEFT', 'FIRE'].includes(coverage.type),
        limitPerDay: coverage.type === 'VEHICLE_REPLACEMENT' ? enhancedLimit : null,
        limitMaxDays: coverage.type === 'VEHICLE_REPLACEMENT' ? 30 : null,
        limitMaxMonths: null,
        liabilityBodilyCap: coverage.type === 'MANDATORY_LIABILITY' ? 70000000 : null,
        liabilityPropertyCap: coverage.type === 'MANDATORY_LIABILITY' ? 15000000 : null,
        deductible: enhancedDeductible,
        deductiblePercent: null,
        description: enhancedDescription
      });
    }
  }

  // Add premium extras for first broker
  if (brokerIndex === 0) {
    const premiumExtras = [
      {
        type: 'VEHICLE_REPLACEMENT',
        customName: 'Vehículo de Sustitución Premium',
        limit: null,
        limitPerDay: 50.00,
        limitMaxDays: 30,
        deductible: null,
        description: 'Vehículo de sustitución de gama similar durante reparaciones, hasta 30 días'
      },
      {
        type: 'TRAVEL_ASSISTANCE',
        customName: 'Asistencia en Viaje Europa+',
        limit: 2000.00,
        deductible: null,
        description: 'Asistencia en viaje extendida a toda Europa con repatriación incluida'
      },
      {
        type: 'PERSONAL_BELONGINGS',
        customName: 'Efectos Personales Plus',
        limit: 800.00,
        deductible: 50.00,
        description: 'Cobertura de efectos personales en el vehículo, incluyendo equipos electrónicos'
      }
    ];

    for (const extra of premiumExtras) {
      bidCoverageData.push({
        bidId: bidId,
        type: extra.type,
        customName: extra.customName,
        limit: extra.limit,
        limitIsUnlimited: false,
        limitIsFullCost: false,
        limitPerDay: extra.limitPerDay || null,
        limitMaxDays: extra.limitMaxDays || null,
        limitMaxMonths: null,
        liabilityBodilyCap: null,
        liabilityPropertyCap: null,
        deductible: extra.deductible,
        deductiblePercent: null,
        description: extra.description
      });
    }
  }

  // Bulk create all bid coverages
  if (bidCoverageData.length > 0) {
    await prisma.bidCoverage.createMany({
      data: bidCoverageData
    });
  }
}

async function main() {
  console.log('Starting database seeding...');

  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.ACCOUNT_HOLDER,
      phone: '+***********',
      profile: {
        firstName: 'María',
        lastName: 'García López',
      },
    },
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.BROKER,
      phone: '+***********',
      profile: {
        firstName: 'Carlos',
        lastName: 'Martínez',
        registrationClass: 'Clase A',
        registrationKey: 'REG_001_2024',
        registrationDate: new Date('2024-01-15'),
        legalName: 'Seguros Martínez S.L.',
        identifier: 'B12345678',
        insurerCompany: InsurerCompany.MAPFRE,
        isAuthorizedByOther: false,
        isComplementary: false,
        isGroupAgent: false,
      },
    },
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.BROKER,
      phone: '+***********',
      profile: {
        firstName: 'Ana',
        lastName: 'Rodríguez',
        registrationClass: 'Clase A',
        registrationKey: 'REG_002_2024',
        registrationDate: new Date('2024-02-20'),
        legalName: null,
        identifier: 'B87654321',
        insurerCompany: InsurerCompany.ALLIANZ,
        isAuthorizedByOther: false,
        isComplementary: false,
        isGroupAgent: false,
      },
    },
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.BROKER,
      phone: '+34611223344',
      profile: {
        firstName: 'Laura',
        lastName: 'Gómez',
        registrationClass: 'Clase B',
        registrationKey: 'REG_003_2024',
        registrationDate: new Date('2024-03-10'),
        legalName: null,
        identifier: 'C98765432',
        insurerCompany: InsurerCompany.AXA,
        isAuthorizedByOther: true,
        isComplementary: false,
        isGroupAgent: false,
      },
    },
    {
      email: '<EMAIL>',
      password: 'Abcdef7*',
      role: Role.ADMIN,
      phone: '+34911234567',
      profile: {
        firstName: 'Admin',
        lastName: 'User',
      },
    },
  ];

  // To ensure a clean slate, delete all existing data in the correct order (respecting foreign key constraints)
  console.log('🗑️  Deleting existing data in correct order...');

  // Use transaction for bulk cleanup operations
  await prisma.$transaction(async (tx) => {
    // Delete dependent records first (in correct order based on foreign key constraints)
    await Promise.all([
      tx.auctionCommission.deleteMany({}),
      tx.auctionWinner.deleteMany({}),
      tx.bidCoverage.deleteMany({}),
      tx.notificationLog.deleteMany({}),
    ]);
    console.log('Deleted auction-related dependent records.');

    await tx.bid.deleteMany({});
    console.log('Deleted all existing bids.');

    await tx.auction.deleteMany({});
    console.log('Deleted all existing auctions.');

    await tx.coverage.deleteMany({});
    console.log('Deleted all existing coverages.');

    await tx.policyInsuredParty.deleteMany({});
    console.log('Deleted all existing policy insured parties.');

    await tx.policy.deleteMany({});
    console.log('Deleted all existing policies.');

    await tx.vehicle.deleteMany({});
    console.log('Deleted all existing vehicles.');

    await tx.asset.deleteMany({});
    console.log('Deleted all existing assets.');

    await tx.documentation.deleteMany({});
    console.log('Deleted all existing documentation.');

    await tx.subscription.deleteMany({});
    console.log('Deleted all existing subscriptions.');

    await tx.address.deleteMany({});
    console.log('Deleted all existing addresses.');

    await tx.insuredParty.deleteMany({});
    console.log('Deleted all existing insured parties.');

    // Delete profile tables
    await Promise.all([
      tx.accountHolderProfile.deleteMany({}),
      tx.brokerProfile.deleteMany({}),
      tx.adminProfile.deleteMany({}),
    ]);
    console.log('Deleted all existing profiles.');

    // Finally delete users
    await tx.user.deleteMany({});
    console.log('Deleted all existing users from the public.User table.');
  });
  
  // Bulk delete Supabase Auth users
  const { data: { users: allUsers }, error: listError } = await supabase.auth.admin.listUsers();

  if (listError) {
    console.error('Error fetching users to delete:', listError);
  } else if (allUsers.length > 0) {
    // Delete users in parallel batches
    const deletePromises = allUsers.map(user =>
      supabase.auth.admin.deleteUser(user.id).then(({ error }) => {
        if (error) {
          console.error(`Error deleting user ${user.email}:`, error);
        } else {
          console.log(`Deleted existing user ${user.email} from Supabase Auth.`);
        }
      })
    );
    await Promise.all(deletePromises);
  }

  // Create users in parallel with optimized approach
  console.log('👥 Creating users and profiles...');
  const userCreationPromises = testUsers.map(async (userData) => {
    try {
      // Create user in Supabase Auth
      const { data: newAuthUser, error: newAuthError } = await supabase.auth.admin.createUser({
        email: userData.email,
        password: userData.password,
        phone: userData.phone,
        email_confirm: true,
        phone_confirm: true,
        user_metadata: {
          display_name: `${userData.profile.firstName} ${userData.profile.lastName}`,
          role: userData.role,
          first_name: userData.profile.firstName,
          last_name: userData.profile.lastName,
          phone: userData.phone,
        },
      });

      if (newAuthError) {
        console.error(`Error creating user ${userData.email} in Supabase Auth:`, newAuthError);
        return null;
      }

      const userId = newAuthUser.user.id;
      console.log(`Created user ${userData.email} in Supabase Auth with ID: ${userId}`);

      // Create user and profile in database transaction
      await prisma.$transaction(async (tx) => {
        // Create user
        await tx.user.create({
          data: {
            id: userId,
            email: userData.email,
            phone: userData.phone,
            firstName: userData.profile.firstName,
            lastName: userData.profile.lastName,
            displayName: `${userData.profile.firstName} ${userData.profile.lastName}`,
            role: userData.role,
          },
        });

        // Create profile based on role
        switch (userData.role) {
          case Role.ACCOUNT_HOLDER:
            await tx.accountHolderProfile.create({
              data: { userId },
            });
            break;

          case Role.BROKER:
            const profileData = userData.profile as any;
            await tx.brokerProfile.create({
              data: {
                userId,
                registrationClass: profileData.registrationClass,
                registrationKey: profileData.registrationKey,
                registrationDate: profileData.registrationDate,
                legalName: profileData.legalName,
                identifier: profileData.identifier,
                insurerCompany: profileData.insurerCompany,
                isAuthorizedByOther: profileData.isAuthorizedByOther,
                isComplementary: profileData.isComplementary,
                isGroupAgent: profileData.isGroupAgent,
              },
            });
            break;

          case Role.ADMIN:
            await tx.adminProfile.create({
              data: { userId },
            });
            break;
        }
      });

      console.log(`Created user ${userData.email} and profile in database.`);
      return { userData, userId };
    } catch (error) {
      console.error(`Failed to create user ${userData.email}:`, error);
      return null;
    }
  });

  const createdUsers = (await Promise.all(userCreationPromises)).filter(Boolean);

  // ============================================================================
  // CREATE COMPREHENSIVE TEST DATA
  // ============================================================================

  console.log('\n🏗️  Creating comprehensive test data...');

  // Use current date for "today" to ensure auctions are always in the future
  const today = new Date();
  today.setHours(10, 0, 0, 0); // Set to 10:00 AM for consistency
  console.log(`🗓️  Using current date for "today": ${today.toISOString()}`);

  // Get the created users for relationships
  const accountHolderUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { accountHolderProfile: true }
  });

  const broker1User = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { brokerProfile: true }
  });

  const broker2User = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { brokerProfile: true }
  });

  const broker3User = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
    include: { brokerProfile: true }
  });

  if (!accountHolderUser?.accountHolderProfile || !broker1User?.brokerProfile || !broker2User?.brokerProfile || !broker3User?.brokerProfile) {
    throw new Error('Required user profiles not found');
  }

  // Create addresses for brokers using bulk operation
  console.log('📍 Creating broker addresses...');
  const brokerAddresses = [
    {
      brokerId: broker1User.brokerProfile.id,
      street: 'Calle Gran Vía, 45, 3º A',
      city: 'Madrid',
      province: 'MADRID' as const,
      region: 'MADRID' as const,
      country: 'SPAIN' as const,
      postalCode: '28013'
    },
    {
      brokerId: broker2User.brokerProfile.id,
      street: 'Avenida Diagonal, 123, 2º B',
      city: 'Barcelona',
      province: 'BARCELONA' as const,
      region: 'CATALONIA' as const,
      country: 'SPAIN' as const,
      postalCode: '08028'
    },
    {
      brokerId: broker3User.brokerProfile.id,
      street: 'Plaza del Ayuntamiento, 1',
      city: 'Valencia',
      province: 'VALENCIA' as const,
      region: 'VALENCIAN_COMMUNITY' as const,
      country: 'SPAIN' as const,
      postalCode: '46002'
    }
  ];

  await prisma.address.createMany({
    data: brokerAddresses
  });

  console.log(`✅ Created addresses for ${broker1User.firstName}, ${broker2User.firstName} and ${broker3User.firstName}`);

  // Create assets for the account holder
  console.log('🚗 Creating assets...');
  const carAsset = await prisma.asset.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      assetType: 'CAR',
      description: 'Seat León 1.5 TSI FR',
      value: 28500.00,
      vehicleDetails: {
        create: {
          brand: 'Seat',
          model: 'León',
          year: 2022,
          firstRegistrationDate: new Date('2022-03-15'),
          licensePlate: '1234ABC',
          version: '1.5 TSI FR',
          fuelType: 'GASOLINE',
          powerCv: 150,
          chassisNumber: 'VSSZZZKJZHXXXXXX',
          isLeased: false,
          seats: 5,
          garageType: 'PRIVATE',
          usageType: 'PRIVATE_REGULAR',
          kmPerYear: 'FROM_10000_TO_12000'
        }
      }
    }
  });

  const motorcycleAsset = await prisma.asset.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      assetType: 'MOTORCYCLE',
      description: 'Yamaha MT-07',
      value: 8500.00,
      vehicleDetails: {
        create: {
          brand: 'Yamaha',
          model: 'MT-07',
          year: 2023,
          firstRegistrationDate: new Date('2023-05-20'),
          licensePlate: '5678DEF',
          version: 'ABS',
          fuelType: 'GASOLINE',
          powerCv: 74.8,
          chassisNumber: 'JYARM321000XXXXXX',
          isLeased: false,
          seats: 2,
          garageType: 'SHARED_GUARDED',
          usageType: 'PRIVATE_OCCASIONAL',
          kmPerYear: 'FROM_4000_TO_6000'
        }
      }
    }
  });

  console.log(`✅ Created car asset: ${carAsset.description} and motorcycle: ${motorcycleAsset.description}`);

  // Create insured parties
  console.log('👥 Creating insured parties...');
  const mainInsuredParty = await prisma.insuredParty.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      firstName: 'María',
      lastName: 'García López',
      displayName: 'María García López',
      identification: '12345678A',
      roles: ['POLICYHOLDER','MAIN_DRIVER','OWNER'],
      gender: Gender.FEMALE,
      birthDate: new Date('1985-03-15'),
      driverLicenseNumber: '12345678A',
      driverLicenseIssuedAt: new Date('2003-04-01'),
      address: {
        create: {
          street: 'Calle Alcalá, 123, 4º C',
          city: 'Madrid',
          province: 'MADRID',
          region: 'MADRID',
          country: 'SPAIN',
          postalCode: '28009'
        }
      }
    }
  });

  const additionalDriver = await prisma.insuredParty.create({
    data: {
      accountHolderId: accountHolderUser.accountHolderProfile.id,
      firstName: 'Juan',
      lastName: 'García Martín',
      displayName: 'Juan García Martín',
      identification: '87654321B',
      roles: ['ADDITIONAL_DRIVER'],
      gender: Gender.MALE,
      birthDate: new Date('1982-07-22'),
      driverLicenseNumber: '87654321B',
      driverLicenseIssuedAt: new Date('2000-10-15'),
      address: {
        create: {
          street: 'Calle Alcalá, 123, 4º C',
          city: 'Madrid',
          province: 'MADRID',
          region: 'MADRID',
          country: 'SPAIN',
          postalCode: '28009'
        }
      }
    }
  });

  console.log(`✅ Created insured parties: ${mainInsuredParty.firstName} ${mainInsuredParty.lastName} and ${additionalDriver.firstName} ${additionalDriver.lastName}`);

  // Create documentation records using bulk operations
  console.log('📄 Creating documentation...');

  // Create policy documents first (needed for policies)
  const policyDocuments = await prisma.$transaction(async (tx) => {
    const carDoc = await tx.documentation.create({
      data: {
        accountHolderId: accountHolderUser.accountHolderProfile!.id,
        type: 'POLICY_DOCUMENT',
        url: '/documents/policies/poliza_coche_2024.pdf',
        fileName: 'poliza_coche_2024.pdf',
        fileSize: 2048576, // 2MB
        mimeType: 'application/pdf'
      }
    });

    const motoDoc = await tx.documentation.create({
      data: {
        accountHolderId: accountHolderUser.accountHolderProfile!.id,
        type: 'POLICY_DOCUMENT',
        url: '/documents/policies/poliza_moto_2024.pdf',
        fileName: 'poliza_moto_2024.pdf',
        fileSize: 1536123, // 1.5MB
        mimeType: 'application/pdf'
      }
    });

    return { carDoc, motoDoc };
  });

  // Create broker quote documents in parallel
  const brokerQuotePromises = [
    prisma.documentation.create({
      data: {
        brokerId: broker1User.brokerProfile.id,
        type: 'QUOTE_DOCUMENT',
        url: '/documents/quotes/cotizacion_moto_broker1.pdf',
        fileName: 'cotizacion_moto_broker1.pdf',
        fileSize: 1024768, // 1MB
        mimeType: 'application/pdf'
      }
    }),
    prisma.documentation.create({
      data: {
        brokerId: broker2User.brokerProfile.id,
        type: 'QUOTE_DOCUMENT',
        url: '/documents/quotes/cotizacion_moto_broker2.pdf',
        fileName: 'cotizacion_moto_broker2.pdf',
        fileSize: 1124768, // 1.1MB
        mimeType: 'application/pdf'
      }
    }),
    prisma.documentation.create({
      data: {
        brokerId: broker3User.brokerProfile.id,
        type: 'QUOTE_DOCUMENT',
        url: '/documents/quotes/cotizacion_moto_broker3.pdf',
        fileName: 'cotizacion_moto_broker3.pdf',
        fileSize: 924768, // 0.9MB
        mimeType: 'application/pdf'
      }
    })
  ];

  await Promise.all(brokerQuotePromises);

  const carPolicyDocument = policyDocuments.carDoc;
  const motorcyclePolicyDocument = policyDocuments.motoDoc;

  console.log('✅ Created documentation records');

  // --- Policy & Auction Creation ---
  console.log('🔄 Creating policies and auctions based on scenarios...');

  const policyScenarios = [
    // Scenario 0: Demonstration of complete "Select Best Offer" workflow
    {
      policyStatus: PolicyStatus.ACTIVE,
      policyEndDateOffset: 25, // Expires in 25 days
      auctionStatus: AuctionState.CLOSED,
      auctionDates: { startOffset: -5, endOffset: -1 }, // Recently closed
      withBids: true,
      asset: carAsset,
      document: carPolicyDocument,
      insuredParties: [mainInsuredParty, additionalDriver],
      coverages: getSimplifiedCoverages(),
      policyNumber: 'POL-WORKFLOW-DEMO',
      // Special flags for workflow demonstration
      demonstrateWorkflow: true,
      selectedBidIndex: 0, // Select the best (lowest) bid
      hasNewPolicyDocument: true,
      isFinalized: true,
    },
    // Scenario 1: Active policy, open auction with bids
    {
      policyStatus: PolicyStatus.ACTIVE,
      policyEndDateOffset: 30, // Expires in 30 days
      auctionStatus: AuctionState.OPEN,
      auctionDates: { startOffset: 0 }, // Starts today, ends based on working hours
      withBids: true,
      asset: carAsset,
      document: carPolicyDocument,
      insuredParties: [mainInsuredParty, additionalDriver],
      insurer: InsurerCompany.MAPFRE,
      policyNumber: 'POL-CAR-2024-001',
    },
    // Scenario 2: Policy renewing soon, open auction without bids
    {
      policyStatus: PolicyStatus.RENEW_SOON,
      policyEndDateOffset: 15, // Expires in 15 days
      auctionStatus: AuctionState.OPEN,
      auctionDates: { startOffset: 0, endOffset: 2 }, // Starts today, ends in 2 days
      withBids: false,
      asset: motorcycleAsset,
      document: motorcyclePolicyDocument,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.MUTUA_MADRILENA,
      policyNumber: 'POL-MOTO-2024-002',
    },
    // Scenario 3: Policy renewing soon, but auction is already closed
    {
      policyStatus: PolicyStatus.RENEW_SOON,
      policyEndDateOffset: 10, // Expires in 10 days
      auctionStatus: AuctionState.CLOSED,
      auctionDates: { startOffset: -10, endOffset: -3 }, // In the past
      withBids: true,
      asset: null, // Create new asset
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.ALLIANZ,
      policyNumber: 'POL-CAR-2024-003',
    },
    // Scenario 4: Active policy, auction resulted in a signed policy
    {
      policyStatus: PolicyStatus.ACTIVE,
      policyEndDateOffset: 50, // Expires in 50 days
      auctionStatus: AuctionState.SIGNED_POLICY,
      auctionDates: { startOffset: -20, endOffset: -13 }, // In the past
      withBids: true,
      asset: null,
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.AXA,
      policyNumber: 'POL-MOTO-2024-004',
    },
    // Scenario 5: Active policy, auction was canceled
    {
      policyStatus: PolicyStatus.ACTIVE,
      policyEndDateOffset: 40, // Expires in 40 days
      auctionStatus: AuctionState.CANCELED,
      auctionDates: { startOffset: -15, endOffset: -8 }, // In the past
      withBids: false,
      asset: null,
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.GENERALI,
      policyNumber: 'POL-CAR-2024-005',
    },
    // Scenario 6: Expired policy, expired auction
    {
      policyStatus: PolicyStatus.EXPIRED,
      policyEndDateOffset: -5, // Expired 5 days ago
      auctionStatus: AuctionState.EXPIRED,
      auctionDates: { startOffset: -40, endOffset: -33 }, // In the past
      withBids: true,
      asset: null,
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.ZURICH,
      policyNumber: 'POL-MOTO-2024-006',
    },
    // Scenario 7: Draft policy, no auction
    {
      policyStatus: PolicyStatus.DRAFT,
      policyEndDateOffset: null,
      auctionStatus: null,
      withBids: false,
      asset: null,
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.SANTALUCIA,
      policyNumber: 'POL-DRAFT-2024-007',
    },
    // Scenario 8: Rejected policy, no auction
    {
      policyStatus: PolicyStatus.REJECTED,
      policyEndDateOffset: null,
      auctionStatus: null,
      auctionDates: {},
      withBids: false,
      asset: null,
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.MAPFRE,
      policyNumber: 'POL-REJECTED-2024-008',
    },
    // Scenario 9: Edge case - Monday early start (05:00 Madrid) - before working hours
    {
      policyStatus: PolicyStatus.ACTIVE,
      policyEndDateOffset: 25,
      auctionStatus: AuctionState.OPEN,
      auctionDates: { customStartTime: 'monday-05:00' }, // Special handling
      withBids: true,
      asset: null,
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.MAPFRE,
      policyNumber: 'POL-EDGE-MONDAY-EARLY',
    },
    // Scenario 10: Late Friday Start (20:00 Madrid) - limited Friday hours
    {
      policyStatus: PolicyStatus.ACTIVE,
      policyEndDateOffset: 25,
      auctionStatus: AuctionState.OPEN,
      auctionDates: { customStartTime: 'friday-20:00' }, // Special handling
      withBids: true,
      asset: null,
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.ALLIANZ,
      policyNumber: 'POL-EDGE-FRIDAY-LATE',
    },
    // Scenario 11: Weekend Saturday Start (10:00 Madrid) - should move to Monday
    {
      policyStatus: PolicyStatus.ACTIVE,
      policyEndDateOffset: 25,
      auctionStatus: AuctionState.OPEN,
      auctionDates: { customStartTime: 'saturday-10:00' }, // Special handling
      withBids: false,
      asset: null,
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.AXA,
      policyNumber: 'POL-EDGE-SATURDAY',
    },
    // Scenario 12: Weekend Sunday Start (03:00 Madrid) - should move to Monday
    {
      policyStatus: PolicyStatus.ACTIVE,
      policyEndDateOffset: 25,
      auctionStatus: AuctionState.OPEN,
      auctionDates: { customStartTime: 'sunday-03:00' }, // Special handling
      withBids: false,
      asset: null,
      document: null,
      insuredParties: [mainInsuredParty],
      insurer: InsurerCompany.GENERALI,
      policyNumber: 'POL-EDGE-SUNDAY-EARLY',
    },
  ];

  let policyCounter = 1;

  for (const scenario of policyScenarios) {
    console.log(`  - Creating policy #${policyCounter} with status: ${scenario.policyStatus}`);

    let asset = scenario.asset;
    if (!asset) {
      const assetType = policyCounter % 2 === 0 ? 'MOTORCYCLE' : 'CAR';
      const brand = assetType === 'CAR' ? 'Audi' : 'Ducati';
      const model = assetType === 'CAR' ? 'A3' : 'Panigale';
      asset = await prisma.asset.create({
        data: {
          accountHolderId: accountHolderUser.accountHolderProfile.id,
          assetType,
          description: `${brand} ${model} (Policy #${policyCounter})`,
          value: 35000.00,
          vehicleDetails: {
            create: {
              brand,
              model,
              year: 2023,
              firstRegistrationDate: new Date('2023-01-01'),
              licensePlate: `AU${policyCounter.toString().padStart(2, '0')}${scenario.policyStatus.substring(0, 2)}`,
              version: 'Sportback',
              fuelType: 'GASOLINE',
              powerCv: 150,
              chassisNumber: `VSSZZZAUZHXXXX${policyCounter.toString().padStart(2, '0')}`,
              isLeased: false,
              seats: 5,
              garageType: 'PRIVATE',
              usageType: 'PRIVATE_REGULAR',
              kmPerYear: 'FROM_10000_TO_12000'
            }
          }
        }
      });
    }

    let document = scenario.document;
    if (!document) {
        document = await prisma.documentation.create({
            data: {
                accountHolderId: accountHolderUser.accountHolderProfile.id,
                type: 'POLICY_DOCUMENT',
                url: `/documents/policies/poliza_scenario_${policyCounter}.pdf`,
                fileName: `poliza_scenario_${policyCounter}.pdf`,
                fileSize: 1000000,
                mimeType: 'application/pdf',
            },
        });
    }

    const policyData: any = {
        documentId: document.id,
        accountHolderId: accountHolderUser.accountHolderProfile.id,
        assetId: asset.id,
        policyNumber: scenario.policyNumber,
        insurerCompany: scenario.insurer,
        status: scenario.policyStatus,
        isAssetsTypeConfirmed: true,
        paymentPeriod: 'ANNUAL',
        premium: 600.00 + policyCounter * 10,
        productName: `Seguro ${asset.assetType === 'CAR' ? 'Coche' : 'Moto'} #${policyCounter}`,
        termsAccepted: true,
        termsAcceptedAt: new Date('2024-01-01T10:00:00Z'),
        insuredParties: {
            create: scenario.insuredParties.map(p => ({ insuredPartyId: p.id })),
        },
    };

    if (scenario.policyEndDateOffset !== null) {
        const policyEndDate = new Date(today);
        policyEndDate.setDate(today.getDate() + scenario.policyEndDateOffset);
        const policyStartDate = new Date(policyEndDate);
        policyStartDate.setFullYear(policyEndDate.getFullYear() - 1);
        policyData.startDate = policyStartDate;
        policyData.endDate = policyEndDate;
    }

    const newPolicy = await prisma.policy.create({ data: policyData });
    console.log(`    ✅ Created policy ${newPolicy.policyNumber}`);

    // Create sample coverages for the policy using bulk operation
    console.log(`    📋 Adding coverages to policy ${newPolicy.policyNumber}...`);

    // Use simplified coverage data with dynamic asset value for specific coverage types
    const processedSampleCoverages = getSimplifiedCoverages().map((coverage: any) => {
      // Normalize mandatory liability coverage
      const normalizedCoverage = normalizeMandatoryLiability(coverage);

      // Set asset value for vehicle-related coverages
      if (normalizedCoverage.type === 'VEHICLE_DAMAGE' || normalizedCoverage.type === 'FIRE' || normalizedCoverage.type === 'THEFT') {
        return {
          ...normalizedCoverage,
          limit: asset.value
        };
      }
      return normalizedCoverage;
    });

    // Create coverages for the policy using bulk operation
    const coverageData = processedSampleCoverages.map((coverage: any) => ({
      policyId: newPolicy.id,
      type: coverage.type as any,
      customName: coverage.customName || null,
      description: coverage.description || null,
      limit: coverage.limit || null,
      limitIsUnlimited: coverage.limitIsUnlimited || false,
      limitIsFullCost: coverage.limitIsFullCost || false,
      limitPerDay: coverage.limitPerDay || null,
      limitMaxDays: coverage.limitMaxDays || null,
      limitMaxMonths: coverage.limitMaxMonths || null,
      liabilityBodilyCap: coverage.liabilityBodilyCap || null,
      liabilityPropertyCap: coverage.liabilityPropertyCap || null,
      deductible: coverage.deductible || null,
      deductiblePercent: coverage.deductiblePercent || null,
    }));

    await prisma.coverage.createMany({
      data: coverageData
    });

    console.log(`    ✅ Added ${processedSampleCoverages.length} coverages to policy ${newPolicy.policyNumber}`);

    if (scenario.auctionStatus) {
        console.log(`    - Creating auction with status: ${scenario.auctionStatus}`);
        let auctionStartDate = new Date(today);

        // Handle custom start times for edge case testing
        if (scenario.auctionDates.customStartTime) {
          const customTime = scenario.auctionDates.customStartTime;
          console.log(`    - Using custom start time: ${customTime}`);

          if (customTime === 'monday-05:00') {
            // Find next Monday and set to 05:00 Madrid time (03:00 UTC in summer)
            const nextMonday = new Date(today);
            nextMonday.setDate(today.getDate() + (1 + 7 - today.getDay()) % 7);
            auctionStartDate = new Date(nextMonday.getFullYear(), nextMonday.getMonth(), nextMonday.getDate(), 3, 0, 0); // 05:00 Madrid = 03:00 UTC
          } else if (customTime === 'friday-20:00') {
            // Find next Friday and set to 20:00 Madrid time (18:00 UTC in summer)
            const nextFriday = new Date(today);
            nextFriday.setDate(today.getDate() + (5 + 7 - today.getDay()) % 7);
            auctionStartDate = new Date(nextFriday.getFullYear(), nextFriday.getMonth(), nextFriday.getDate(), 18, 0, 0); // 20:00 Madrid = 18:00 UTC
          } else if (customTime === 'saturday-10:00') {
            // Find next Saturday and set to 10:00 Madrid time (08:00 UTC in summer)
            const nextSaturday = new Date(today);
            nextSaturday.setDate(today.getDate() + (6 + 7 - today.getDay()) % 7);
            auctionStartDate = new Date(nextSaturday.getFullYear(), nextSaturday.getMonth(), nextSaturday.getDate(), 8, 0, 0); // 10:00 Madrid = 08:00 UTC
          } else if (customTime === 'sunday-03:00') {
            // Find next Sunday and set to 03:00 Madrid time (01:00 UTC in summer)
            const nextSunday = new Date(today);
            nextSunday.setDate(today.getDate() + (7 - today.getDay()) % 7);
            if (nextSunday.getTime() === today.getTime()) nextSunday.setDate(nextSunday.getDate() + 7);
            auctionStartDate = new Date(nextSunday.getFullYear(), nextSunday.getMonth(), nextSunday.getDate(), 1, 0, 0); // 03:00 Madrid = 01:00 UTC
          }
        } else if (scenario.auctionDates.startOffset !== undefined) {
          auctionStartDate.setDate(today.getDate() + scenario.auctionDates.startOffset);
        }

        // Calculate auction end date using working hours business logic (48 working hours, Mon-Fri 06:00-23:59 Madrid time)
        const auctionEndDate = calculateWorkingHoursClosedAt(auctionStartDate, DEFAULT_AUCTION_DURATION_HOURS);

        console.log(`    - Auction start: ${auctionStartDate.toISOString()} (Madrid: ${auctionStartDate.toLocaleString('sv-SE', { timeZone: 'Europe/Madrid' })})`);
        console.log(`    - Auction end date: ${auctionEndDate.toISOString()} (Madrid: ${auctionEndDate.toLocaleString('sv-SE', { timeZone: 'Europe/Madrid' })})`);

        const newAuction = await prisma.auction.create({
            data: {
                accountHolderId: accountHolderUser.accountHolderProfile.id,
                policyId: newPolicy.id,
                status: scenario.auctionStatus,
                startDate: auctionStartDate,
                endDate: auctionEndDate,
                maxWinners: 3,
                minWinners: 1,
            },
        });
        console.log(`    ✅ Created auction ${newAuction.id} for policy ${newPolicy.policyNumber}`);

        await prisma.documentation.update({
            where: { id: document.id },
            data: { relatedAuctionId: newAuction.id },
        });

        if (scenario.withBids) {
            console.log(`    💰 Adding bids to auction ${newAuction.id}...`);

            // Get policy coverages once for all bids
            const policyCoverages = await prisma.coverage.findMany({
              where: { policyId: newPolicy.id }
            });

            // Define broker strategies
            const brokerStrategies = [
              {
                name: 'Premium Plus',
                description: 'Cobertura premium con beneficios adicionales',
                limitMultiplier: 1.15,
                deductibleMultiplier: 0.8,
                includeRate: 0.95
              },
              {
                name: 'Value Optimizer',
                description: 'Optimización de valor con términos competitivos',
                limitMultiplier: 1.05,
                deductibleMultiplier: 0.9,
                includeRate: 0.9
              },
              {
                name: 'Essential Coverage',
                description: 'Cobertura esencial con precios ajustados',
                limitMultiplier: 0.95,
                deductibleMultiplier: 1.1,
                includeRate: 0.85
              }
            ];

            const brokers = [broker1User, broker2User, broker3User];

            // Create bids and their coverages in parallel
            const bidPromises = brokers.map(async (broker, i) => {
              if (!broker?.brokerProfile) return;

              const bidDate = new Date(newAuction.startDate);
              bidDate.setHours(bidDate.getHours() + (i + 1) * 2);

              const initialBid = await prisma.bid.create({
                data: {
                  auctionId: newAuction.id,
                  brokerId: broker.brokerProfile.id,
                  amount: (newPolicy.premium?.toNumber() ?? 600) * (0.9 - i * 0.05), // 10%, 15%, 20% discount
                  createdAt: bidDate,
                },
              });

              const strategy = brokerStrategies[i % brokerStrategies.length]!;

              // Create bid coverages using optimized helper function
              await createBidCoveragesForBid(
                prisma,
                initialBid.id,
                policyCoverages,
                strategy,
                i
              );

              return initialBid;
            });

            await Promise.all(bidPromises);
            console.log(`    ✅ Added 3 bids to auction ${newAuction.id}`);
        }

        // Handle workflow demonstration scenario
        if (scenario.demonstrateWorkflow && scenario.withBids) {
            console.log(`    🎯 Setting up workflow demonstration for auction ${newAuction.id}...`);

            // Get the bids for this auction to select the best one
            const auctionBids = await prisma.bid.findMany({
                where: { auctionId: newAuction.id },
                orderBy: { amount: 'asc' }, // Get lowest price first
                include: {
                    broker: {
                        include: { user: true }
                    }
                }
            });

            if (auctionBids.length > 0) {
                const selectedBidIndex = scenario.selectedBidIndex || 0;
                const selectedBid = auctionBids[selectedBidIndex];

                if (selectedBid) {
                    // Create a new policy document for the workflow
                    let newPolicyDocumentId = null;
                    if (scenario.hasNewPolicyDocument) {
                        const newPolicyDoc = await prisma.documentation.create({
                            data: {
                                fileName: `nueva-poliza-${newPolicy.policyNumber}.pdf`,
                                url: `https://example.com/documents/nueva-poliza-${newPolicy.policyNumber}.pdf`,
                                fileSize: 2048576, // 2MB
                                mimeType: 'application/pdf',
                                type: DocumentType.NEW_POLICY_DOCUMENT,
                                accountHolderId: accountHolderUser.accountHolderProfile.id,
                                relatedAuctionId: newAuction.id,
                                isPolicyAttested: true,
                                policyAttestedAt: new Date(),
                            },
                        });
                        newPolicyDocumentId = newPolicyDoc.id;
                        console.log(`      📄 Created new policy document ${newPolicyDoc.id}`);
                    }

                    // Update auction with workflow fields
                    const workflowData: any = {
                        selectedBidId: selectedBid.id,
                        selectedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
                        signatureConfirmed: true,
                        signatureConfirmedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
                    };

                    if (newPolicyDocumentId) {
                        workflowData.newPolicyDocumentId = newPolicyDocumentId;
                    }

                    if (scenario.isFinalized) {
                        workflowData.finalizedAt = new Date(Date.now() - 30 * 60 * 1000); // 30 minutes ago
                        workflowData.status = 'SIGNED_POLICY';
                    }

                    await prisma.auction.update({
                        where: { id: newAuction.id },
                        data: workflowData,
                    });

                    console.log(`      ✅ Updated auction with selected bid ${selectedBid.id} from ${selectedBid.broker.user.displayName}`);
                    console.log(`      💰 Selected bid amount: €${selectedBid.amount}`);
                    console.log(`      📋 Workflow status: ${scenario.isFinalized ? 'Finalized' : 'In Progress'}`);
                }
            }
        }
    }
    policyCounter++;
  }

  // Add 15 additional bids specifically for POL-CAR-2024-001
  console.log('💰 Adding 15 additional bids to POL-CAR-2024-001...');
  
  // Declare additionalBrokers at function scope to be accessible in summary
  const additionalBrokers = [];
  
  const pol001Auction = await prisma.auction.findFirst({
    where: {
      policy: {
        policyNumber: 'POL-CAR-2024-001'
      }
    },
    include: {
      policy: true
    }
  });

  if (pol001Auction) {
    // Create additional brokers for more bids
    const brokerCompanies = [
      'MAPFRE', 'ALLIANZ', 'AXA', 'GENERALI', 'ZURICH', 'SANTALUCIA', 'DIRECT_SEGUROS',
      'MUTUA_MADRILENA', 'PELAYO', 'CATALANA_OCCIDENTE', 'LIBERTY_SEGUROS', 'REALE_SEGUROS', 'CASER',
      'FIATC', 'HELVETIA'
    ];
    
    const brokerFirstNames = [
      'Carlos', 'María', 'José', 'Ana', 'Francisco', 'Carmen', 'Antonio', 'Isabel',
      'Manuel', 'Pilar', 'David', 'Rosa', 'Miguel', 'Elena', 'Rafael'
    ];
    
    const brokerLastNames = [
      'García López', 'Martínez Ruiz', 'González Pérez', 'Rodríguez Sánchez', 'López Martín',
      'Hernández Gil', 'Pérez Moreno', 'Sánchez Jiménez', 'Ruiz Álvarez', 'Jiménez Romero',
      'Moreno Castro', 'Muñoz Ortega', 'Álvarez Rubio', 'Romero Iglesias', 'Gutierrez Vargas'
    ];
    
    const brokerCompanyNames = [
       'Seguros Premium', 'Broker Elite', 'Aseguradoras Top', 'Seguros Express',
       'Broker Master', 'Seguros Plus', 'Broker Pro', 'Seguros Direct',
       'Broker Smart', 'Seguros Fast', 'Broker Expert', 'Seguros Quality',
       'Broker Select', 'Seguros Prime', 'Broker Advanced'
     ];

    // Create additional brokers in parallel
    const additionalBrokerPromises = Array.from({ length: 15 }, async (_, i) => {
      const brokerEmail = `broker.extra${i + 4}@zeeguros.com`;
      const brokerPassword = 'password123';

      try {
        // Create user in Supabase Auth
        const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
          email: brokerEmail,
          password: brokerPassword,
          email_confirm: true,
          user_metadata: {
            role: 'BROKER',
            firstName: brokerFirstNames[i],
            lastName: brokerLastNames[i]
          }
        });

        if (authError) {
          console.error(`Failed to create auth user for ${brokerEmail}:`, authError);
          return null;
        }

        const userId = authUser.user.id;
        console.log(`Created user ${brokerEmail} in Supabase Auth with ID: ${userId}`);

        // Create user and broker profile in transaction
        const brokerUser = await prisma.$transaction(async (tx) => {
          const user = await tx.user.create({
            data: {
              id: userId,
              email: brokerEmail,
              firstName: brokerFirstNames[i],
              lastName: brokerLastNames[i],
              displayName: `${brokerFirstNames[i]} ${brokerLastNames[i]}`,
              role: 'BROKER',
            }
          });

          await tx.brokerProfile.create({
            data: {
              userId,
              registrationClass: 'BROKER',
              registrationKey: `REG-EXTRA-${(i + 4).toString().padStart(3, '0')}`,
              registrationDate: new Date(),
              legalName: brokerCompanyNames[i],
              identifier: `ID-EXTRA-${(i + 4).toString().padStart(3, '0')}`,
              insurerCompany: brokerCompanies[i] as InsurerCompany,
              isComplementary: false,
              isGroupAgent: false,
              kycStatus: 'VERIFIED',
              billingAddress: {
                create: {
                  street: `Calle Broker Extra ${i + 4}`,
                  city: 'Madrid',
                  province: 'MADRID' as const,
                  region: 'MADRID' as const,
                  postalCode: `280${(i % 10).toString().padStart(2, '0')}`
                }
              }
            }
          });

          return user;
        });

        console.log(`Created user and broker profile for ${brokerEmail}`);
        return brokerUser;
      } catch (error) {
        console.error(`Failed to create broker ${brokerEmail}:`, error);
        return null;
      }
    });

    const createdAdditionalBrokers = (await Promise.all(additionalBrokerPromises)).filter(Boolean);
    additionalBrokers.push(...createdAdditionalBrokers);

    // Create 15 additional bids with varying amounts and timestamps
    const basePremium = pol001Auction.policy.premium?.toNumber() ?? 600;
    const auctionStart = new Date(pol001Auction.startDate);

    // Get broker profiles for the additional brokers
    const additionalBrokerProfiles = await prisma.brokerProfile.findMany({
      where: {
        userId: {
          in: additionalBrokers.map(b => b!.id)
        }
      }
    });

    // Create bids in parallel for better performance
    const additionalBidPromises = additionalBrokerProfiles.map(async (brokerProfile, i) => {
      // Create varied bid amounts (discounts from 5% to 35%)
      const discountPercent = 0.05 + (i * 0.02); // 5%, 7%, 9%, ... up to 35%
      const bidAmount = basePremium * (1 - discountPercent);

      // Create varied bid timestamps throughout the auction period
      const bidDate = new Date(auctionStart);
      bidDate.setHours(auctionStart.getHours() + 6 + (i * 3)); // Spread bids over time
      bidDate.setMinutes(Math.floor(Math.random() * 60)); // Random minutes

      const newBid = await prisma.bid.create({
        data: {
          auctionId: pol001Auction.id,
          brokerId: brokerProfile.id,
          amount: Math.round(bidAmount * 100) / 100, // Round to 2 decimal places
          createdAt: bidDate,
        },
      });

      return newBid;
    });

    // Get policy coverages once for all additional bids
    const policyCoverages = await prisma.coverage.findMany({
      where: { policyId: pol001Auction.policy.id }
    });

    // Define advanced broker strategies
    const advancedStrategies = [
      {
        name: 'Digital First',
        description: 'Solución digital innovadora',
        limitMultiplier: 1.2,
        deductibleMultiplier: 0.75,
        includeRate: 0.92,
        specialization: 'TECH_ENHANCED'
      },
      {
        name: 'Customer Care Plus',
        description: 'Atención al cliente excepcional',
        limitMultiplier: 1.1,
        deductibleMultiplier: 0.85,
        includeRate: 0.95,
        specialization: 'SERVICE_FOCUSED'
      },
      {
        name: 'Risk Optimizer',
        description: 'Gestión avanzada de riesgos',
        limitMultiplier: 1.08,
        deductibleMultiplier: 0.9,
        includeRate: 0.88,
        specialization: 'RISK_MANAGEMENT'
      },
      {
        name: 'Green Insurance',
        description: 'Seguros sostenibles y ecológicos',
        limitMultiplier: 1.12,
        deductibleMultiplier: 0.8,
        includeRate: 0.9,
        specialization: 'ECO_FRIENDLY'
      },
      {
        name: 'Fleet Specialist',
        description: 'Especialista en flotas y vehículos comerciales',
        limitMultiplier: 1.15,
        deductibleMultiplier: 0.85,
        includeRate: 0.93,
        specialization: 'COMMERCIAL'
      }
    ];

    // Wait for all bids to be created
    const createdBids = await Promise.all(additionalBidPromises);

    // Create bid coverages for all bids in parallel
    const bidCoveragePromises = createdBids.map(async (bid, i) => {
      if (!bid) return;

      const strategy = advancedStrategies[i % advancedStrategies.length]!;

      // Use the optimized helper function for creating bid coverages
      await createBidCoveragesForBid(
        prisma,
        bid.id,
        policyCoverages,
        strategy,
        i
      );
    });

    await Promise.all(bidCoveragePromises);

    console.log(`    ✅ Added 15 additional bids to POL-CAR-2024-001 auction`);
  }

  // Create auction winners for the first scenario
  console.log('🏆 Creating auction winners...');
  const firstAuctionWithBids = await prisma.auction.findFirst({
    where: {
      policy: {
        policyNumber: 'POL-CAR-2024-001'
      }
    },
    include: {
      bids: {
        orderBy: {
          amount: 'asc'
        }
      },
      policy: true
    }
  });

  if (firstAuctionWithBids && firstAuctionWithBids.bids.length >= 3) {
    const bestBids = firstAuctionWithBids.bids;
    const winnerBroker1 = bestBids[0];
    const winnerBroker2 = bestBids[1];
    const winnerBroker3 = bestBids[2];

    if (winnerBroker1 && winnerBroker2 && winnerBroker3) {
        const winner1 = await prisma.auctionWinner.create({
          data: {
            auctionId: firstAuctionWithBids.id,
            brokerId: winnerBroker1.brokerId,
            bidId: winnerBroker1.id,
            position: 1
          }
        });
        const winner2 = await prisma.auctionWinner.create({
          data: {
            auctionId: firstAuctionWithBids.id,
            brokerId: winnerBroker2.brokerId,
            bidId: winnerBroker2.id,
            position: 2
          }
        });
        const winner3 = await prisma.auctionWinner.create({
          data: {
            auctionId: firstAuctionWithBids.id,
            brokerId: winnerBroker3.brokerId,
            bidId: winnerBroker3.id,
            position: 3
          }
        });
        console.log(`✅ Created auction winners for auction ${firstAuctionWithBids.id}`);

        // Simulate one winner paying the commission
        console.log('💸 Simulating commission payment...');
        await prisma.auctionCommission.create({
          data: {
            auctionId: firstAuctionWithBids.id,
            winnerId: winner1.id,
            brokerId: winner1.brokerId,
            amount: (firstAuctionWithBids.policy.premium?.toNumber() ?? 0) * 0.1, // 10% commission
            status: 'PAID',
            paidAt: new Date(),
            stripePaymentIntentId: 'pi_3P...' // Fake payment intent
          }
        });

        // Update winner to reflect data reveal
        await prisma.auctionWinner.update({
          where: { id: winner1.id },
          data: { contactDataRevealedAt: new Date() }
        });
        console.log(`✅ Commission paid by winner and contact data revealed.`);
    }
  }


  // Create a subscription for a broker
  console.log('💳 Creating broker subscription...');
  await prisma.subscription.create({
    data: {
      brokerId: broker1User.brokerProfile.id,
      stripeSubscriptionId: 'sub_1P...',
      status: 'active',
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    }
  });

  console.log(`✅ Created subscription for ${broker1User.firstName}`);

  // Calculate dynamic counts for accurate summary
  const totalUsers = testUsers.length + additionalBrokers.length;
  const initialBrokers = testUsers.filter(user => user.role === Role.BROKER).length;
  const totalBrokers = initialBrokers + additionalBrokers.length;
  const accountHolders = testUsers.filter(user => user.role === Role.ACCOUNT_HOLDER).length;
  const admins = testUsers.filter(user => user.role === Role.ADMIN).length;
  
  console.log('\n🎉 Comprehensive test data creation completed!');
  console.log('📊 Summary of created data:');
  console.log(`   - ${totalUsers} Users (${accountHolders} account holder, ${totalBrokers} brokers, ${admins} admin)`);
  console.log(`   - ${policyScenarios.length + 2} Assets (cars, motorcycles) with vehicle details`);
  console.log('   - 2 Insured parties with addresses');
  console.log(`   - ${policyScenarios.length} Policies with various statuses`);
  console.log(`   - ${policyScenarios.filter(p => p.auctionStatus).length} Auctions with various statuses`);
  console.log('   - Multiple bids on auctions with bids');
  console.log('   - 3 Broker addresses');
  console.log('   - Multiple documentation records (policies and quotes)');
  console.log('   - 1 Broker subscription');
  console.log('   - Auction winners and commission payments for testing');

  console.log('\n🔧 Infrastructure components deployed:');
  console.log('   - Auction expiration cron job (every 5 minutes)');
  console.log('   - Comprehensive notification system');
  console.log('   - Auction winner failsafe trigger (migration 006)');
  console.log('   - sendAuctionNotification Edge Function');
  console.log('   - Environment variable security configuration');

  console.log('\n✅ Database seeding completed successfully!');
  console.log('🚀 All auction automation systems are active and ready!');
}

main()
  .catch((e) => {
    console.error('Error during database seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
