import { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { createClient } from "@/lib/supabase/server";
import { AuctionState } from "@prisma/client";
import { getCurrentUser } from "@/lib/api-auth";
import { validatePolicyDocument } from "@/lib/file-validation";
import { createSuccessResponse, handleApiError, ApiResponses } from "@/lib/api-responses";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user using centralized utility
    const user = await getCurrentUser(request);

    const formData = await request.formData();
    const file = formData.get("file") as File;
    const fileName = formData.get("fileName") as string;

    // Validate file using centralized utility
    validatePolicyDocument(file);

    // Verify auction exists and belongs to the user
    const auction = await db.auction.findFirst({
      where: {
        id: params.id,
        accountHolder: {
          userId: user.id,
        },
        status: AuctionState.CLOSED,
      },
      include: {
        policy: true,
        accountHolder: true,
      },
    });

    if (!auction) {
      return ApiResponses.notFound(
        "Subasta no encontrada o no autorizada",
        "AUCTION_NOT_FOUND"
      );
    }

    // Upload file to Supabase Storage
    const supabase = await createClient();
    const fileBuffer = await file.arrayBuffer();
    const fileExtension = file.name.split('.').pop();
    const uniqueFileName = `${auction.accountHolderId}/policies/${Date.now()}-${fileName || 'policy'}.${fileExtension}`;

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('documents')
      .upload(uniqueFileName, fileBuffer, {
        contentType: file.type,
        upsert: false,
      });

    if (uploadError) {
      return ApiResponses.internalServerError(
        "Error al subir el archivo",
        "UPLOAD_FAILED"
      );
    }

    // Get the public URL
    const { data: { publicUrl } } = supabase.storage
      .from('documents')
      .getPublicUrl(uniqueFileName);

    // Create documentation record with attestation
    const documentation = await db.documentation.create({
      data: {
        fileName: fileName || file.name,
        url: publicUrl,
        fileSize: file.size,
        mimeType: file.type,
        type: "NEW_POLICY_DOCUMENT",
        accountHolderId: auction.accountHolderId,
        relatedAuctionId: auction.id,
        isPolicyAttested: true,
        policyAttestedAt: new Date(),
      },
    });

    // Update auction with new policy document link
    await db.auction.update({
      where: {
        id: auction.id,
      },
      data: {
        newPolicyDocumentId: documentation.id,
      },
    });

    // Update policy with new document
    await db.policy.update({
      where: {
        id: auction.policyId,
      },
      data: {
        documentId: documentation.id,
      },
    });

    return createSuccessResponse(
      { documentation },
      "Póliza subida exitosamente"
    );
  } catch (error) {
    return handleApiError(error);
  }
}